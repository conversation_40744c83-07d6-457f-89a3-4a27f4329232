# Microsoft Teams Chat Message Scraper

This tool extracts messages from Microsoft Teams chat pages that load dynamically. It handles scrolling to load earlier messages and exports all messages to CSV files.

## Features

- **Dynamic Content Handling**: Automatically scrolls to load older messages
- **Duplicate Prevention**: Filters out duplicate messages
- **Multiple Output Formats**: Creates both detailed and simple CSV files
- **System Message Filtering**: Removes UI elements and system messages
- **Robust Selector Strategy**: Uses multiple CSS selectors to find messages

## Installation

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Playwright browsers**:
   ```bash
   playwright install chromium
   ```

## Usage

1. **Run the scraper**:
   ```bash
   python teams_chat_scraper.py
   ```

2. **Enter the Teams chat URL** when prompted

3. **Login to Teams** when the browser opens (if required)

4. **Wait for the scraper** to automatically scroll and collect messages

## Output Files

The scraper creates two CSV files:

- **`teams_messages.csv`**: Detailed version with timestamps and metadata
- **`teams_messages_simple.csv`**: Simple version with just message text (one message per row)

## How It Works

1. **Browser Automation**: Uses Playwright to control a Chromium browser
2. **Dynamic Loading**: Scrolls up repeatedly to trigger loading of older messages
3. **Message Extraction**: Uses multiple CSS selectors to find message elements
4. **Filtering**: Removes system messages and UI elements
5. **Deduplication**: Ensures each unique message is only saved once
6. **CSV Export**: Saves messages in CSV format for easy analysis

## Configuration

You can modify these parameters in the `main()` function:

- **`max_scrolls`**: Maximum number of scroll attempts (default: 100)
- **`output_file`**: Name of the output CSV file
- **`headless`**: Set to `True` to run browser in background

## Troubleshooting

### Common Issues

1. **Login Required**: The browser will open and you may need to log in to Teams
2. **Slow Loading**: Increase wait times if messages load slowly
3. **Missing Messages**: Increase `max_scrolls` parameter
4. **Access Denied**: Ensure you have permission to view the chat

### Debugging

- Set `headless=False` to see the browser in action
- Check console output for progress updates
- Examine the generated CSV files to verify results

## Notes

- The scraper respects Teams' loading patterns and waits appropriately
- It's designed to be gentle on the server with reasonable delays
- Works best with standard Teams chat interfaces
- May require adjustments for custom Teams configurations

## Legal Considerations

- Only scrape chats you have legitimate access to
- Respect your organization's data policies
- Consider privacy implications of extracted messages
- Use responsibly and in compliance with Terms of Service
