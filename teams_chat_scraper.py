"""
Microsoft Teams Chat Message Scraper

This script extracts messages from a Microsoft Teams chat page that loads dynamically.
It handles scrolling to load earlier messages and exports all messages to a CSV file.
"""

import asyncio
import csv
import time
from datetime import datetime
from playwright.async_api import async_playwright
import pandas as pd


class TeamsChatScraper:
    def __init__(self):
        self.messages = []
        self.seen_messages = set()  # To avoid duplicates
        
    async def scrape_teams_chat(self, url, output_file="teams_messages.csv", max_scrolls=50):
        """
        Scrape messages from Microsoft Teams chat page
        
        Args:
            url (str): The Teams chat URL
            output_file (str): Output CSV filename
            max_scrolls (int): Maximum number of scroll attempts to load older messages
        """
        async with async_playwright() as p:
            # Launch browser - use chromium for better Teams compatibility
            browser = await p.chromium.launch(headless=False)  # Set to True for headless mode
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                print(f"Navigating to: {url}")
                await page.goto(url, wait_until="networkidle")
                
                # Wait for the chat to load
                await page.wait_for_timeout(3000)
                
                # Try to find the main chat container
                chat_selectors = [
                    '[data-tid="chat-pane-message"]',
                    '.ui-chat__messagelist',
                    '[role="log"]',
                    '.ts-message-list-container',
                    '.message-list',
                    '.ui-chat__item'
                ]
                
                chat_container = None
                for selector in chat_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        chat_container = selector
                        print(f"Found chat container with selector: {selector}")
                        break
                    except:
                        continue
                
                if not chat_container:
                    print("Could not find chat container. Let's try to find messages directly...")
                
                # Scroll up to load older messages
                print("Starting to scroll and collect messages...")
                scroll_count = 0
                no_new_messages_count = 0
                
                while scroll_count < max_scrolls and no_new_messages_count < 5:
                    # Get current messages
                    previous_count = len(self.messages)
                    await self.extract_messages_from_page(page)
                    
                    # Check if we got new messages
                    if len(self.messages) == previous_count:
                        no_new_messages_count += 1
                    else:
                        no_new_messages_count = 0
                        print(f"Found {len(self.messages)} total messages so far...")
                    
                    # Scroll up to load older messages
                    await page.keyboard.press("Home")  # Go to top
                    await page.wait_for_timeout(1000)
                    
                    # Alternative scrolling methods
                    await page.evaluate("window.scrollTo(0, 0)")
                    await page.wait_for_timeout(1000)
                    
                    scroll_count += 1
                    
                    if scroll_count % 10 == 0:
                        print(f"Completed {scroll_count} scroll attempts, found {len(self.messages)} messages")
                
                # Final extraction
                await self.extract_messages_from_page(page)
                
                print(f"Scraping completed! Found {len(self.messages)} total messages")
                
            except Exception as e:
                print(f"Error during scraping: {e}")
                # Try to extract whatever we can
                await self.extract_messages_from_page(page)
                
            finally:
                await browser.close()
        
        # Save to CSV
        self.save_to_csv(output_file)
        return len(self.messages)
    
    async def extract_messages_from_page(self, page):
        """Extract messages from the current page state"""
        
        # Multiple selectors to try for message extraction
        message_selectors = [
            # Teams specific selectors
            '[data-tid="chat-pane-message"]',
            '.ui-chat__message',
            '.ts-message',
            '.message-body',
            '.ui-chat__messagecontent',
            
            # Generic message selectors
            '[role="listitem"]',
            '.message',
            '.chat-message',
            
            # Text content selectors
            'div[data-tid*="message"]',
            'div[class*="message"]'
        ]
        
        for selector in message_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector: {selector}")
                    
                    for element in elements:
                        try:
                            # Get text content
                            text = await element.inner_text()
                            text = text.strip()
                            
                            if text and len(text) > 0 and text not in self.seen_messages:
                                # Filter out system messages and UI elements
                                if not self.is_system_message(text):
                                    self.messages.append({
                                        'message': text,
                                        'timestamp': datetime.now().isoformat(),
                                        'selector_used': selector
                                    })
                                    self.seen_messages.add(text)
                        except Exception as e:
                            continue
                    
                    if elements:  # If we found messages with this selector, continue with it
                        break
                        
            except Exception as e:
                continue
    
    def is_system_message(self, text):
        """Filter out system messages and UI elements"""
        system_indicators = [
            'Type a message',
            'Meet now',
            'Shared',
            'Chat',
            'Unread',
            'Mentions',
            'Favorites',
            'More options',
            'Search',
            'New chat',
            'Calendar',
            'Calls',
            'Files',
            'Wiki',
            'Planner'
        ]
        
        # Skip very short messages or UI elements
        if len(text) < 3:
            return True
            
        # Skip messages that are just timestamps or system indicators
        for indicator in system_indicators:
            if text.lower().startswith(indicator.lower()) or text.lower() == indicator.lower():
                return True
                
        return False
    
    def save_to_csv(self, filename):
        """Save messages to CSV file"""
        if not self.messages:
            print("No messages found to save!")
            return
            
        # Remove duplicates and sort by timestamp
        unique_messages = []
        seen = set()
        
        for msg in self.messages:
            if msg['message'] not in seen:
                unique_messages.append(msg)
                seen.add(msg['message'])
        
        # Save to CSV
        df = pd.DataFrame(unique_messages)
        df.to_csv(filename, index=False, encoding='utf-8')
        
        # Also save a simple version with just messages
        simple_filename = filename.replace('.csv', '_simple.csv')
        with open(simple_filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['message'])  # Header
            for msg in unique_messages:
                writer.writerow([msg['message']])
        
        print(f"Saved {len(unique_messages)} unique messages to:")
        print(f"  - Detailed: {filename}")
        print(f"  - Simple: {simple_filename}")


async def main():
    """Main function to run the scraper"""
    scraper = TeamsChatScraper()
    
    # You'll need to replace this with your actual Teams chat URL
    teams_url = input("Enter the Microsoft Teams chat URL: ").strip()
    
    if not teams_url:
        print("Please provide a valid Teams chat URL")
        return
    
    print("Starting Teams chat scraper...")
    print("Note: You may need to log in to Teams when the browser opens")
    print("The scraper will automatically scroll and collect messages")
    
    try:
        message_count = await scraper.scrape_teams_chat(
            url=teams_url,
            output_file="teams_messages.csv",
            max_scrolls=100  # Adjust based on how many messages you expect
        )
        
        print(f"\nScraping completed successfully!")
        print(f"Extracted {message_count} messages")
        print("Check the generated CSV files for the results")
        
    except Exception as e:
        print(f"Error running scraper: {e}")


if __name__ == "__main__":
    asyncio.run(main())
