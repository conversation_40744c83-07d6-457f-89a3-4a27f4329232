"""
Microsoft Teams Chat Message Scraper

This script extracts messages from a Microsoft Teams chat page that loads dynamically.
It handles scrolling to load earlier messages and exports all messages to a CSV file.
"""

import asyncio
import csv
import time
from datetime import datetime
from playwright.async_api import async_playwright
import pandas as pd


class TeamsChatScraper:
    def __init__(self):
        self.messages = []
        self.seen_messages = set()  # To avoid duplicates
        
    async def scrape_teams_chat(self, output_file="teams_messages.csv", max_scrolls=50):
        """
        Scrape messages from Microsoft Teams chat page

        Args:
            output_file (str): Output CSV filename
            max_scrolls (int): Maximum number of scroll attempts to load older messages
        """
        async with async_playwright() as p:
            # Launch browser - use chromium for better Teams compatibility
            browser = await p.chromium.launch(headless=False)  # Set to True for headless mode
            context = await browser.new_context()
            page = await context.new_page()

            try:
                print("Navigating to Microsoft Teams...")
                await page.goto("https://teams.microsoft.com", wait_until="networkidle")
                
                # Wait for Teams to load and user to navigate to chat
                print("\n" + "="*60)
                print("INSTRUCTIONS:")
                print("1. Please log in to Microsoft Teams if prompted")
                print("2. Navigate to the chat you want to scrape")
                print("3. Make sure the chat messages are visible")
                print("4. Press ENTER in this terminal when ready to start scraping")
                print("="*60)

                # Wait for user confirmation
                input("Press ENTER when you're ready to start scraping...")

                print("Starting to scrape messages...")
                await page.wait_for_timeout(2000)
                
                # Scroll up to load older messages
                print("Starting to scroll and collect messages...")
                scroll_count = 0
                no_new_messages_count = 0
                
                while scroll_count < max_scrolls and no_new_messages_count < 5:
                    # Get current messages
                    previous_count = len(self.messages)
                    await self.extract_messages_from_page(page)
                    
                    # Check if we got new messages
                    if len(self.messages) == previous_count:
                        no_new_messages_count += 1
                    else:
                        no_new_messages_count = 0
                        print(f"Found {len(self.messages)} total messages so far...")
                    
                    # Scroll up to load older messages - Teams specific scrolling
                    # Try multiple scrolling methods for Teams
                    await page.keyboard.press("PageUp")
                    await page.wait_for_timeout(500)

                    await page.keyboard.press("PageUp")
                    await page.wait_for_timeout(500)

                    # Also try scrolling the chat container specifically
                    await page.evaluate("""
                        // Find the chat container and scroll it
                        const chatContainer = document.querySelector('[role="main"]') ||
                                            document.querySelector('.ui-chat__messagelist') ||
                                            document.querySelector('[data-tid="chat-pane"]');
                        if (chatContainer) {
                            chatContainer.scrollTop = Math.max(0, chatContainer.scrollTop - 1000);
                        }
                        // Also try general window scroll
                        window.scrollBy(0, -1000);
                    """)
                    await page.wait_for_timeout(1500)  # Give more time for Teams to load
                    
                    scroll_count += 1
                    
                    if scroll_count % 10 == 0:
                        print(f"Completed {scroll_count} scroll attempts, found {len(self.messages)} messages")
                
                # Final extraction
                await self.extract_messages_from_page(page)
                
                print(f"Scraping completed! Found {len(self.messages)} total messages")
                
            except Exception as e:
                print(f"Error during scraping: {e}")
                # Try to extract whatever we can
                await self.extract_messages_from_page(page)
                
            finally:
                await browser.close()
        
        # Save to CSV
        self.save_to_csv(output_file)
        return len(self.messages)
    
    async def extract_messages_from_page(self, page):
        """Extract messages from the current page state"""

        # Based on the Teams structure observed, look for message groups
        try:
            # Look for message groups - these contain the actual message content
            message_groups = await page.query_selector_all('group[aria-label*="PM."], group[aria-label*="AM."]')

            if message_groups:
                print(f"Found {len(message_groups)} message groups")

                for group in message_groups:
                    try:
                        # Get the aria-label which contains sender, message, and timestamp
                        aria_label = await group.get_attribute('aria-label')

                        if aria_label and aria_label not in self.seen_messages:
                            # Extract just the message text from the aria-label
                            # Format is usually: "Sender message_text Date at Time."
                            message_text = self.extract_message_from_aria_label(aria_label)

                            if message_text and not self.is_system_message(message_text):
                                self.messages.append({
                                    'message': message_text,
                                    'full_aria_label': aria_label,
                                    'timestamp': datetime.now().isoformat(),
                                    'selector_used': 'group[aria-label]'
                                })
                                self.seen_messages.add(aria_label)

                    except Exception:
                        continue

            # Fallback: Look for paragraph elements that contain message text
            if not message_groups:
                print("No message groups found, trying paragraph elements...")
                paragraphs = await page.query_selector_all('p')

                for p in paragraphs:
                    try:
                        text = await p.inner_text()
                        text = text.strip()

                        if text and len(text) > 2 and text not in self.seen_messages:
                            if not self.is_system_message(text):
                                self.messages.append({
                                    'message': text,
                                    'timestamp': datetime.now().isoformat(),
                                    'selector_used': 'paragraph'
                                })
                                self.seen_messages.add(text)

                    except Exception:
                        continue

        except Exception as e:
            print(f"Error extracting messages: {e}")

    def extract_message_from_aria_label(self, aria_label):
        """Extract just the message text from the aria-label"""
        try:
            # Aria-label format: "Sender message_text Date at Time."
            # We want to extract just the message_text part

            # Split by common patterns to isolate the message
            parts = aria_label.split(' ')

            # Find where the actual message starts (after sender name)
            # and where it ends (before date/time)
            message_parts = []

            for i, part in enumerate(parts):
                # Skip sender name (usually first part before the message)
                if i == 0:
                    continue

                # Stop when we hit date patterns
                if any(pattern in part.lower() for pattern in ['yesterday', 'today', 'august', 'september', 'january', 'february', 'march', 'april', 'may', 'june', 'july', 'october', 'november', 'december']):
                    break

                # Stop when we hit time patterns
                if ':' in part and ('AM' in part or 'PM' in part or 'am' in part or 'pm' in part):
                    break

                # Stop when we hit "at" before time
                if part.lower() == 'at':
                    break

                message_parts.append(part)

            message = ' '.join(message_parts).strip()

            # Clean up common artifacts
            message = message.rstrip('.')

            return message if len(message) > 0 else aria_label

        except Exception:
            return aria_label
    
    def is_system_message(self, text):
        """Filter out system messages and UI elements"""
        system_indicators = [
            'Type a message',
            'Meet now',
            'Shared',
            'Chat',
            'Unread',
            'Mentions',
            'Favorites',
            'More options',
            'Search',
            'New chat',
            'Calendar',
            'Calls',
            'Files',
            'Wiki',
            'Planner'
        ]
        
        # Skip very short messages or UI elements
        if len(text) < 3:
            return True
            
        # Skip messages that are just timestamps or system indicators
        for indicator in system_indicators:
            if text.lower().startswith(indicator.lower()) or text.lower() == indicator.lower():
                return True
                
        return False
    
    def save_to_csv(self, filename):
        """Save messages to CSV file"""
        if not self.messages:
            print("No messages found to save!")
            return
            
        # Remove duplicates and sort by timestamp
        unique_messages = []
        seen = set()
        
        for msg in self.messages:
            if msg['message'] not in seen:
                unique_messages.append(msg)
                seen.add(msg['message'])
        
        # Save to CSV
        df = pd.DataFrame(unique_messages)
        df.to_csv(filename, index=False, encoding='utf-8')
        
        # Also save a simple version with just messages
        simple_filename = filename.replace('.csv', '_simple.csv')
        with open(simple_filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['message'])  # Header
            for msg in unique_messages:
                writer.writerow([msg['message']])
        
        print(f"Saved {len(unique_messages)} unique messages to:")
        print(f"  - Detailed: {filename}")
        print(f"  - Simple: {simple_filename}")


async def main():
    """Main function to run the scraper"""
    scraper = TeamsChatScraper()

    print("Starting Teams chat scraper...")
    print("A browser will open to teams.microsoft.com")
    print("You'll need to log in and navigate to your chat manually")

    try:
        message_count = await scraper.scrape_teams_chat(
            output_file="teams_messages.csv",
            max_scrolls=100  # Adjust based on how many messages you expect
        )
        
        print(f"\nScraping completed successfully!")
        print(f"Extracted {message_count} messages")
        print("Check the generated CSV files for the results")
        
    except Exception as e:
        print(f"Error running scraper: {e}")


if __name__ == "__main__":
    asyncio.run(main())
